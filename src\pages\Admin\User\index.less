.user-management {
  .search-area {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    
    .ant-form-item {
      margin-bottom: 0;
    }
  }
  
  .header-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .title {
      margin: 0;
    }
    
    .actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .user-table {
    .ant-table-thead > tr > th {
      background: #f5f5f5;
      font-weight: 600;
    }
    
    .user-avatar {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .role-tag {
      font-weight: 500;
    }
    
    .status-tag {
      font-weight: 500;
    }
    
    .action-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }
  
  .user-modal {
    .form-note {
      background: #f6f6f6;
      padding: 12px;
      border-radius: 6px;
      margin-bottom: 16px;
      
      p {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }
    
    .ant-form-item-label > label {
      font-weight: 500;
    }
  }
  
  // 响应式设计
  @media (max-width: 768px) {
    .header-actions {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
      
      .title {
        text-align: center;
      }
      
      .actions {
        justify-content: center;
      }
    }
    
    .search-area {
      .ant-form {
        .ant-form-item {
          margin-bottom: 16px;
        }
        
        .ant-form-item:last-child {
          margin-bottom: 0;
        }
      }
    }
    
    .user-table {
      .ant-table {
        font-size: 12px;
      }
      
      .action-buttons {
        flex-direction: column;
        gap: 4px;
        
        .ant-btn {
          padding: 4px 8px;
          height: auto;
          font-size: 12px;
        }
      }
    }
  }
  
  @media (max-width: 480px) {
    padding: 12px;
    
    .search-area {
      padding: 12px;
    }
    
    .user-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 4px;
      }
    }
  }
}

// 全局样式优化
.ant-modal {
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;
  }
  
  .ant-modal-title {
    font-weight: 600;
  }
}

.ant-table {
  .ant-table-pagination {
    margin-top: 16px;
    text-align: right;
  }
}

// 加载状态样式
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

// 空状态样式
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #999;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .empty-text {
    font-size: 16px;
  }
}
