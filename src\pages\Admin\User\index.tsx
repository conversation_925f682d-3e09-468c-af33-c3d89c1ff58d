import {
  createUser,
  deleteUser,
  getUserList,
  updateUser,
  UserInfo,
  UserListResponse,
} from '@/services/user';
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Button,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Switch,
  Table,
  Tag,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import './index.less';
import { ColumnsType } from 'antd/es/table';

const { Title } = Typography;

const AdminUser: React.FC = () => {
  const [data, setData] = useState<UserInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<UserInfo | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();

  // 分页相关状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 搜索关键词
  const [keyword, setKeyword] = useState('');

  // 获取用户列表
  const fetchUserList = async (page = 1, pageSize = 10, searchKeyword = '') => {
    setLoading(true);
    try {
      const response = await getUserList({
        page,
        pageSize,
        keyword: searchKeyword,
      });

      if (response.errCode === 0 && response.data) {
        setData(response.data.data);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.msg || '获取用户列表失败');
      }
    } catch (error: any) {
      console.error('获取用户列表失败:', error);
      message.error(error?.response?.data?.msg || '获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchUserList();
  }, []);

  const handleAdd = () => {
    setEditingItem(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record: UserInfo) => {
    setEditingItem(record);
    // 不显示密码，设置enabled字段
    const { password, ...formData } = record as any;
    void password;
    form.setFieldsValue(formData);
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    // 查找要删除的用户
    const userToDelete = data.find(user => user.id === id);

    // 检查是否是admin账号
    if (userToDelete && userToDelete.username === 'admin') {
      message.error('admin账号不允许删除！');
      return;
    }

    if (data.length <= 1) {
      message.error('至少需要保留一个用户！');
      return;
    }

    try {
      const response = await deleteUser(id);
      if (response.errCode === 0) {
        message.success('删除成功！');
        // 重新获取当前页数据
        fetchUserList(pagination.current, pagination.pageSize, keyword);
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error: any) {
      console.error('删除用户失败:', error);
      message.error(error?.response?.data?.msg || '删除失败');
    }
  };

  const handleResetPassword = (record: UserInfo) => {
    Modal.confirm({
      title: '重置密码',
      content: `确定要将用户 ${record.username} 的密码重置为 "123456" 吗？`,
      async onOk() {
        try {
          const response = await updateUser(record.id, { password: '123456' });
          if (response.errCode === 0) {
            message.success('密码重置成功！新密码为：123456');
          } else {
            message.error(response.msg || '重置密码失败');
          }
        } catch (error: any) {
          console.error('重置密码失败:', error);
          message.error(error?.response?.data?.msg || '重置密码失败');
        }
      },
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (editingItem) {
        // 检查是否是admin账号且尝试禁用
        if (editingItem.username === 'admin' && values.isActive === false) {
          message.error('admin账号不允许禁用！');
          return;
        }

        // 编辑用户（不修改密码）
        try {
          const response = await updateUser(editingItem.id, values);
          if (response.errCode === 0) {
            message.success('编辑成功！');
            setModalVisible(false);
            // 重新获取当前页数据
            fetchUserList(pagination.current, pagination.pageSize, keyword);
          } else {
            message.error(response.msg || '编辑失败');
          }
        } catch (error: any) {
          console.error('编辑用户失败:', error);
          message.error(error?.response?.data?.msg || '编辑失败');
        }
      } else {
        // 新增用户
        try {
          const response = await createUser(values);
          if (response.errCode === 0) {
            message.success('添加成功！');
            setModalVisible(false);
            // 重新获取第一页数据
            fetchUserList(1, pagination.pageSize, keyword);
          } else {
            message.error(response.msg || '添加失败');
          }
        } catch (error: any) {
          console.error('添加用户失败:', error);
          message.error(error?.response?.data?.msg || '添加失败');
        }
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 搜索处理
  const handleSearch = () => {
    const searchKeyword = searchForm.getFieldValue('keyword') || '';
    setKeyword(searchKeyword);
    fetchUserList(1, pagination.pageSize, searchKeyword);
  };

  // 重置搜索
  const handleResetSearch = () => {
    searchForm.resetFields();
    setKeyword('');
    fetchUserList(1, pagination.pageSize, '');
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchUserList(pagination.current, pagination.pageSize, keyword);
  };

  const columns:ColumnsType<UserInfo> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (username: string) => (
        <Space className="user-avatar">
          <UserOutlined />
          {username}
        </Space>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => {
        const color = role === 'admin' ? 'red' : 'blue';
        const text = role === 'admin' ? '管理员' : '用户';
        return <Tag color={color} className="role-tag">{text}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'} className="status-tag">
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (createdAt: string) => {
        return new Date(createdAt).toLocaleString('zh-CN');
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      render: (_: any, record: UserInfo) => (
        <Space size="middle" className="action-buttons">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button type="link" onClick={() => handleResetPassword(record)}>
            重置密码
          </Button>
          <Popconfirm
            title="确定要删除这个用户吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
            disabled={record.username === 'admin'}
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              disabled={record.username === 'admin'}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="user-management" style={{ padding: '24px' }}>
      <div className="header-actions">
        <Title level={2} className="title">用户管理</Title>
        <Space className="actions">
          <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
            刷新
          </Button>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            添加用户
          </Button>
        </Space>
      </div>

      {/* 搜索区域 */}
      <div className="search-area">
        <Form form={searchForm} layout="inline">
          <Form.Item name="keyword" label="搜索关键词">
            <Input
              placeholder="请输入用户名搜索"
              style={{ width: 200 }}
              onPressEnter={handleSearch}
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleResetSearch}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </div>

      <Table
        className="user-table"
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 个用户`,
          onChange: (page, pageSize) => {
            fetchUserList(page, pageSize, keyword);
          },
        }}
      />

      <Modal
        title={editingItem ? '编辑用户' : '添加用户'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={500}
        destroyOnClose
        className="user-modal"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            role: 'user',
            isActive: true,
          }}
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, max: 50, message: '用户名长度为3-50字符' },
            ]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          {!editingItem && (
            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, max: 50, message: '密码长度为6-50字符' },
              ]}
            >
              <Input.Password placeholder="请输入密码" />
            </Form.Item>
          )}

          <Form.Item
            name="role"
            label="角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select placeholder="请选择角色">
              <Select.Option value="admin">管理员</Select.Option>
              <Select.Option value="user">用户</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="isActive"
            label="状态"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="启用"
              unCheckedChildren="禁用"
              disabled={editingItem?.username === 'admin'}
            />
          </Form.Item>

          {editingItem && (
            <div className="form-note">
              <p>
                注意：编辑用户不会修改密码，如需重置密码请使用&quot;重置密码&quot;功能
              </p>
              {editingItem.username === 'admin' && (
                <p style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
                  特别提醒：admin账号不允许禁用或删除
                </p>
              )}
            </div>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default AdminUser;
